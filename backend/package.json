{"name": "fb-group-clone-backend", "version": "1.0.0", "description": "Backend API for Facebook Group Clone with Apify Integration", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "echo \"Error: no test specified\" && exit 1", "migrate": "ts-node src/database/migrate.ts"}, "keywords": ["facebook", "groups", "apify", "social", "api"], "author": "", "license": "MIT", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.18.2", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "pg": "^8.16.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/multer": "^2.0.0", "@types/node": "^24.0.13", "@types/pg": "^8.15.4", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}