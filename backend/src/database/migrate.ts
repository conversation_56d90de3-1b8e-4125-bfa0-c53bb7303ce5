import fs from 'fs';
import path from 'path';
import { query, testConnection } from './connection';

const MIGRATIONS_DIR = path.join(__dirname, '../../migrations');

interface Migration {
  id: number;
  name: string;
  filename: string;
  sql: string;
}

// Create migrations table if it doesn't exist
const createMigrationsTable = async (): Promise<void> => {
  const createTableSQL = `
    CREATE TABLE IF NOT EXISTS migrations (
      id SERIAL PRIMARY KEY,
      name VARCHAR(255) NOT NULL UNIQUE,
      executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );
  `;
  
  await query(createTableSQL);
  console.log('✅ Migrations table ready');
};

// Get executed migrations
const getExecutedMigrations = async (): Promise<string[]> => {
  const result = await query('SELECT name FROM migrations ORDER BY id');
  return result.rows.map((row: any) => row.name);
};

// Get pending migrations
const getPendingMigrations = async (): Promise<Migration[]> => {
  // Create migrations directory if it doesn't exist
  if (!fs.existsSync(MIGRATIONS_DIR)) {
    fs.mkdirSync(MIGRATIONS_DIR, { recursive: true });
    console.log('📁 Created migrations directory');
  }

  const executedMigrations = await getExecutedMigrations();
  const migrationFiles = fs.readdirSync(MIGRATIONS_DIR)
    .filter(file => file.endsWith('.sql'))
    .sort();

  const pendingMigrations: Migration[] = [];

  for (const filename of migrationFiles) {
    const name = path.basename(filename, '.sql');
    
    if (!executedMigrations.includes(name)) {
      const filePath = path.join(MIGRATIONS_DIR, filename);
      const sql = fs.readFileSync(filePath, 'utf8');
      
      // Extract migration ID from filename (assuming format: 001_migration_name.sql)
      const idMatch = filename.match(/^(\d+)_/);
      const id = idMatch ? parseInt(idMatch[1]!) : 0;
      
      pendingMigrations.push({
        id,
        name,
        filename,
        sql
      });
    }
  }

  return pendingMigrations.sort((a, b) => a.id - b.id);
};

// Execute a single migration
const executeMigration = async (migration: Migration): Promise<void> => {
  try {
    console.log(`🔄 Executing migration: ${migration.name}`);
    
    // Execute the migration SQL
    await query(migration.sql);
    
    // Record the migration as executed
    await query(
      'INSERT INTO migrations (name) VALUES ($1)',
      [migration.name]
    );
    
    console.log(`✅ Migration completed: ${migration.name}`);
  } catch (error) {
    console.error(`❌ Migration failed: ${migration.name}`, error);
    throw error;
  }
};

// Run all pending migrations
export const runMigrations = async (): Promise<void> => {
  try {
    console.log('🚀 Starting database migrations...');
    
    // Test database connection
    const isConnected = await testConnection();
    if (!isConnected) {
      throw new Error('Database connection failed');
    }
    
    // Create migrations table
    await createMigrationsTable();
    
    // Get pending migrations
    const pendingMigrations = await getPendingMigrations();
    
    if (pendingMigrations.length === 0) {
      console.log('✅ No pending migrations');
      return;
    }
    
    console.log(`📋 Found ${pendingMigrations.length} pending migration(s)`);
    
    // Execute each migration
    for (const migration of pendingMigrations) {
      await executeMigration(migration);
    }
    
    console.log('🎉 All migrations completed successfully!');
    
  } catch (error) {
    console.error('💥 Migration process failed:', error);
    process.exit(1);
  }
};

// CLI interface
if (require.main === module) {
  runMigrations()
    .then(() => {
      console.log('Migration process finished');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration process failed:', error);
      process.exit(1);
    });
}
