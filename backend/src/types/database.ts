// Database model types

export interface User {
  id: string;
  email: string;
  password_hash: string;
  name: string;
  profile_url?: string;
  avatar_url?: string;
  location?: string;
  work?: string;
  bio?: string;
  is_admin: boolean;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface CreateUserInput {
  email: string;
  password: string;
  name: string;
  profile_url?: string;
  avatar_url?: string;
  location?: string;
  work?: string;
  bio?: string;
}

export interface UpdateUserInput {
  name?: string;
  profile_url?: string;
  avatar_url?: string;
  location?: string;
  work?: string;
  bio?: string;
}

export interface Group {
  id: string;
  name: string;
  description?: string;
  privacy_type: 'public' | 'private' | 'secret';
  member_count: number;
  cover_image_url?: string;
  created_by?: string;
  imported_from?: string;
  original_group_id?: string;
  created_at: Date;
  updated_at: Date;
}

export interface CreateGroupInput {
  name: string;
  description?: string;
  privacy_type?: 'public' | 'private' | 'secret';
  cover_image_url?: string;
  created_by: string;
}

export interface UpdateGroupInput {
  name?: string;
  description?: string;
  privacy_type?: 'public' | 'private' | 'secret';
  cover_image_url?: string;
}

export interface GroupMember {
  id: string;
  group_id: string;
  user_id: string;
  role: 'admin' | 'moderator' | 'member';
  joined_date: Date;
}

export interface Post {
  id: string;
  group_id: string;
  author_id?: string;
  content: string;
  post_type: 'text' | 'image' | 'video' | 'link' | 'poll';
  media_urls?: string[];
  hashtags?: string[];
  likes_count: number;
  comments_count: number;
  shares_count: number;
  is_pinned: boolean;
  original_post_id?: string;
  original_timestamp?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface CreatePostInput {
  group_id: string;
  author_id: string;
  content: string;
  post_type?: 'text' | 'image' | 'video' | 'link' | 'poll';
  media_urls?: string[];
  hashtags?: string[];
}

export interface UpdatePostInput {
  content?: string;
  media_urls?: string[];
  hashtags?: string[];
  is_pinned?: boolean;
}

export interface Comment {
  id: string;
  post_id: string;
  author_id?: string;
  parent_comment_id?: string;
  content: string;
  likes_count: number;
  original_comment_id?: string;
  original_timestamp?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface CreateCommentInput {
  post_id: string;
  author_id: string;
  parent_comment_id?: string;
  content: string;
}

export interface UpdateCommentInput {
  content: string;
}

export interface Reaction {
  id: string;
  user_id: string;
  post_id?: string;
  comment_id?: string;
  reaction_type: 'like' | 'love' | 'haha' | 'wow' | 'sad' | 'angry';
  created_at: Date;
}

export interface CreateReactionInput {
  user_id: string;
  post_id?: string;
  comment_id?: string;
  reaction_type: 'like' | 'love' | 'haha' | 'wow' | 'sad' | 'angry';
}

export interface ImportLog {
  id: string;
  group_id?: string;
  imported_by?: string;
  file_name?: string;
  file_size?: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  total_posts: number;
  imported_posts: number;
  total_comments: number;
  imported_comments: number;
  total_users: number;
  imported_users: number;
  error_message?: string;
  started_at: Date;
  completed_at?: Date;
}

export interface CreateImportLogInput {
  group_id?: string;
  imported_by?: string;
  file_name?: string;
  file_size?: number;
}

export interface UpdateImportLogInput {
  status?: 'pending' | 'processing' | 'completed' | 'failed';
  total_posts?: number;
  imported_posts?: number;
  total_comments?: number;
  imported_comments?: number;
  total_users?: number;
  imported_users?: number;
  error_message?: string;
  completed_at?: Date;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T = any> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Query options
export interface QueryOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
  search?: string;
}

// JWT Payload
export interface JWTPayload {
  userId: string;
  email: string;
  name: string;
  isAdmin: boolean;
  iat?: number;
  exp?: number;
}
