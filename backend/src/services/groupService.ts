import { query, transaction } from '../database/connection';
import { Group, CreateGroupInput, UpdateGroupInput, GroupMember } from '../types/database';

export class GroupService {
  // Create a new group
  static async createGroup(groupData: CreateGroupInput): Promise<Group> {
    const { name, description, privacy_type = 'public', cover_image_url, created_by } = groupData;
    
    return await transaction(async (client) => {
      // Create the group
      const groupResult = await client.query(
        `INSERT INTO groups (name, description, privacy_type, cover_image_url, created_by, member_count)
         VALUES ($1, $2, $3, $4, $5, 1)
         RETURNING *`,
        [name, description, privacy_type, cover_image_url, created_by]
      );
      
      const group = groupResult.rows[0];
      
      // Add creator as admin member
      await client.query(
        `INSERT INTO group_members (group_id, user_id, role)
         VALUES ($1, $2, 'admin')`,
        [group.id, created_by]
      );
      
      return group;
    });
  }

  // Get group by ID
  static async findById(id: string): Promise<Group | null> {
    const result = await query(
      'SELECT * FROM groups WHERE id = $1',
      [id]
    );
    
    return result.rows[0] || null;
  }

  // Get group with member info
  static async getGroupWithMemberInfo(groupId: string, userId?: string): Promise<{
    group: Group;
    memberInfo?: GroupMember;
    memberCount: number;
  } | null> {
    const groupResult = await query(
      'SELECT * FROM groups WHERE id = $1',
      [groupId]
    );
    
    if (groupResult.rows.length === 0) {
      return null;
    }
    
    const group = groupResult.rows[0];
    
    // Get member info if user is provided
    let memberInfo = undefined;
    if (userId) {
      const memberResult = await query(
        'SELECT * FROM group_members WHERE group_id = $1 AND user_id = $2',
        [groupId, userId]
      );
      memberInfo = memberResult.rows[0];
    }
    
    // Get actual member count
    const countResult = await query(
      'SELECT COUNT(*) as count FROM group_members WHERE group_id = $1',
      [groupId]
    );
    const memberCount = parseInt(countResult.rows[0].count);
    
    return {
      group,
      memberInfo,
      memberCount
    };
  }

  // Update group
  static async updateGroup(id: string, groupData: UpdateGroupInput): Promise<Group | null> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    // Build dynamic update query
    Object.entries(groupData).forEach(([key, value]) => {
      if (value !== undefined) {
        fields.push(`${key} = $${paramCount}`);
        values.push(value);
        paramCount++;
      }
    });

    if (fields.length === 0) {
      return await this.findById(id);
    }

    values.push(id);
    
    const result = await query(
      `UPDATE groups SET ${fields.join(', ')} WHERE id = $${paramCount} RETURNING *`,
      values
    );
    
    return result.rows[0] || null;
  }

  // Get groups with pagination and filters
  static async getGroups(options: {
    page?: number;
    limit?: number;
    search?: string;
    privacy_type?: 'public' | 'private' | 'secret';
    userId?: string; // For filtering user's groups
  } = {}): Promise<{
    groups: Group[];
    total: number;
  }> {
    const { page = 1, limit = 20, search, privacy_type, userId } = options;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE 1=1';
    const queryParams: any[] = [];
    let paramCount = 1;

    // Add search filter
    if (search) {
      whereClause += ` AND (g.name ILIKE $${paramCount} OR g.description ILIKE $${paramCount})`;
      queryParams.push(`%${search}%`);
      paramCount++;
    }

    // Add privacy filter
    if (privacy_type) {
      whereClause += ` AND g.privacy_type = $${paramCount}`;
      queryParams.push(privacy_type);
      paramCount++;
    }

    // Add user filter (groups user is member of)
    let joinClause = '';
    if (userId) {
      joinClause = 'INNER JOIN group_members gm ON g.id = gm.group_id';
      whereClause += ` AND gm.user_id = $${paramCount}`;
      queryParams.push(userId);
      paramCount++;
    }

    // Get groups
    const groupsResult = await query(
      `SELECT g.*, 
              COALESCE(mc.member_count, 0) as actual_member_count
       FROM groups g 
       ${joinClause}
       LEFT JOIN (
         SELECT group_id, COUNT(*) as member_count 
         FROM group_members 
         GROUP BY group_id
       ) mc ON g.id = mc.group_id
       ${whereClause}
       ORDER BY g.created_at DESC
       LIMIT $${paramCount} OFFSET $${paramCount + 1}`,
      [...queryParams, limit, offset]
    );

    // Get total count
    const countResult = await query(
      `SELECT COUNT(DISTINCT g.id) as total 
       FROM groups g ${joinClause} ${whereClause}`,
      queryParams
    );

    return {
      groups: groupsResult.rows,
      total: parseInt(countResult.rows[0].total)
    };
  }

  // Join group
  static async joinGroup(groupId: string, userId: string): Promise<boolean> {
    try {
      return await transaction(async (client) => {
        // Check if user is already a member
        const existingMember = await client.query(
          'SELECT id FROM group_members WHERE group_id = $1 AND user_id = $2',
          [groupId, userId]
        );
        
        if (existingMember.rows.length > 0) {
          return false; // Already a member
        }
        
        // Add user as member
        await client.query(
          'INSERT INTO group_members (group_id, user_id, role) VALUES ($1, $2, $3)',
          [groupId, userId, 'member']
        );
        
        // Update member count
        await client.query(
          'UPDATE groups SET member_count = member_count + 1 WHERE id = $1',
          [groupId]
        );
        
        return true;
      });
    } catch (error) {
      console.error('Error joining group:', error);
      return false;
    }
  }

  // Leave group
  static async leaveGroup(groupId: string, userId: string): Promise<boolean> {
    try {
      return await transaction(async (client) => {
        // Check if user is a member
        const memberResult = await client.query(
          'SELECT role FROM group_members WHERE group_id = $1 AND user_id = $2',
          [groupId, userId]
        );
        
        if (memberResult.rows.length === 0) {
          return false; // Not a member
        }
        
        const memberRole = memberResult.rows[0].role;
        
        // Don't allow the last admin to leave
        if (memberRole === 'admin') {
          const adminCount = await client.query(
            'SELECT COUNT(*) as count FROM group_members WHERE group_id = $1 AND role = $2',
            [groupId, 'admin']
          );
          
          if (parseInt(adminCount.rows[0].count) <= 1) {
            throw new Error('Cannot leave group: You are the last admin');
          }
        }
        
        // Remove user from group
        await client.query(
          'DELETE FROM group_members WHERE group_id = $1 AND user_id = $2',
          [groupId, userId]
        );
        
        // Update member count
        await client.query(
          'UPDATE groups SET member_count = member_count - 1 WHERE id = $1',
          [groupId]
        );
        
        return true;
      });
    } catch (error) {
      console.error('Error leaving group:', error);
      throw error;
    }
  }

  // Get group members
  static async getGroupMembers(groupId: string, options: {
    page?: number;
    limit?: number;
    role?: 'admin' | 'moderator' | 'member';
  } = {}): Promise<{
    members: Array<GroupMember & { user: any }>;
    total: number;
  }> {
    const { page = 1, limit = 50, role } = options;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE gm.group_id = $1';
    const queryParams: any[] = [groupId];
    let paramCount = 2;

    if (role) {
      whereClause += ` AND gm.role = $${paramCount}`;
      queryParams.push(role);
      paramCount++;
    }

    // Get members with user info
    const membersResult = await query(
      `SELECT gm.*, 
              u.id as user_id, u.name as user_name, u.email as user_email, 
              u.avatar_url as user_avatar_url, u.created_at as user_created_at
       FROM group_members gm
       JOIN users u ON gm.user_id = u.id
       ${whereClause}
       ORDER BY 
         CASE gm.role 
           WHEN 'admin' THEN 1 
           WHEN 'moderator' THEN 2 
           ELSE 3 
         END,
         gm.joined_date ASC
       LIMIT $${paramCount} OFFSET $${paramCount + 1}`,
      [...queryParams, limit, offset]
    );

    // Get total count
    const countResult = await query(
      `SELECT COUNT(*) as total FROM group_members gm ${whereClause}`,
      queryParams
    );

    // Format the results
    const members = membersResult.rows.map(row => ({
      id: row.id,
      group_id: row.group_id,
      user_id: row.user_id,
      role: row.role,
      joined_date: row.joined_date,
      user: {
        id: row.user_id,
        name: row.user_name,
        email: row.user_email,
        avatar_url: row.user_avatar_url,
        created_at: row.user_created_at
      }
    }));

    return {
      members,
      total: parseInt(countResult.rows[0].total)
    };
  }

  // Update member role
  static async updateMemberRole(groupId: string, userId: string, newRole: 'admin' | 'moderator' | 'member'): Promise<boolean> {
    try {
      const result = await query(
        'UPDATE group_members SET role = $1 WHERE group_id = $2 AND user_id = $3',
        [newRole, groupId, userId]
      );
      
      return result.rowCount > 0;
    } catch (error) {
      console.error('Error updating member role:', error);
      return false;
    }
  }

  // Delete group (admin only)
  static async deleteGroup(id: string): Promise<boolean> {
    try {
      const result = await query(
        'DELETE FROM groups WHERE id = $1',
        [id]
      );
      
      return result.rowCount > 0;
    } catch (error) {
      console.error('Error deleting group:', error);
      return false;
    }
  }
}
