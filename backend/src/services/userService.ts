import bcrypt from 'bcryptjs';
import { query } from '../database/connection';
import { User, CreateUserInput, UpdateUserInput } from '../types/database';

export class UserService {
  // Create a new user
  static async createUser(userData: CreateUserInput): Promise<User> {
    const { email, password, name, profile_url, avatar_url, location, work, bio } = userData;
    
    // Hash password
    const saltRounds = 12;
    const password_hash = await bcrypt.hash(password, saltRounds);
    
    const result = await query(
      `INSERT INTO users (email, password_hash, name, profile_url, avatar_url, location, work, bio)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
       RETURNING *`,
      [email, password_hash, name, profile_url, avatar_url, location, work, bio]
    );
    
    return result.rows[0];
  }

  // Find user by email
  static async findByEmail(email: string): Promise<User | null> {
    const result = await query(
      'SELECT * FROM users WHERE email = $1 AND is_active = true',
      [email]
    );
    
    return result.rows[0] || null;
  }

  // Find user by ID
  static async findById(id: string): Promise<User | null> {
    const result = await query(
      'SELECT * FROM users WHERE id = $1 AND is_active = true',
      [id]
    );
    
    return result.rows[0] || null;
  }

  // Update user
  static async updateUser(id: string, userData: UpdateUserInput): Promise<User | null> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    // Build dynamic update query
    Object.entries(userData).forEach(([key, value]) => {
      if (value !== undefined) {
        fields.push(`${key} = $${paramCount}`);
        values.push(value);
        paramCount++;
      }
    });

    if (fields.length === 0) {
      return await this.findById(id);
    }

    values.push(id);
    
    const result = await query(
      `UPDATE users SET ${fields.join(', ')} WHERE id = $${paramCount} AND is_active = true RETURNING *`,
      values
    );
    
    return result.rows[0] || null;
  }

  // Verify password
  static async verifyPassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
    return await bcrypt.compare(plainPassword, hashedPassword);
  }

  // Check if email exists
  static async emailExists(email: string): Promise<boolean> {
    const result = await query(
      'SELECT id FROM users WHERE email = $1',
      [email]
    );
    
    return result.rows.length > 0;
  }

  // Get user profile (without sensitive data)
  static async getUserProfile(id: string): Promise<Omit<User, 'password_hash'> | null> {
    const result = await query(
      `SELECT id, email, name, profile_url, avatar_url, location, work, bio, 
              is_admin, is_active, created_at, updated_at 
       FROM users WHERE id = $1 AND is_active = true`,
      [id]
    );
    
    return result.rows[0] || null;
  }

  // Get multiple users (for admin or group member lists)
  static async getUsers(options: {
    page?: number;
    limit?: number;
    search?: string;
  } = {}): Promise<{
    users: Omit<User, 'password_hash'>[];
    total: number;
  }> {
    const { page = 1, limit = 20, search } = options;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE is_active = true';
    const queryParams: any[] = [];
    let paramCount = 1;

    if (search) {
      whereClause += ` AND (name ILIKE $${paramCount} OR email ILIKE $${paramCount})`;
      queryParams.push(`%${search}%`);
      paramCount++;
    }

    // Get users
    const usersResult = await query(
      `SELECT id, email, name, profile_url, avatar_url, location, work, bio, 
              is_admin, is_active, created_at, updated_at 
       FROM users ${whereClause}
       ORDER BY created_at DESC
       LIMIT $${paramCount} OFFSET $${paramCount + 1}`,
      [...queryParams, limit, offset]
    );

    // Get total count
    const countResult = await query(
      `SELECT COUNT(*) as total FROM users ${whereClause}`,
      queryParams
    );

    return {
      users: usersResult.rows,
      total: parseInt(countResult.rows[0].total)
    };
  }

  // Deactivate user (soft delete)
  static async deactivateUser(id: string): Promise<boolean> {
    const result = await query(
      'UPDATE users SET is_active = false WHERE id = $1',
      [id]
    );
    
    return result.rowCount > 0;
  }

  // Get user statistics
  static async getUserStats(userId: string): Promise<{
    postsCount: number;
    commentsCount: number;
    groupsCount: number;
  }> {
    const result = await query(
      `SELECT 
        (SELECT COUNT(*) FROM posts WHERE author_id = $1) as posts_count,
        (SELECT COUNT(*) FROM comments WHERE author_id = $1) as comments_count,
        (SELECT COUNT(*) FROM group_members WHERE user_id = $1) as groups_count`,
      [userId]
    );

    const stats = result.rows[0];
    return {
      postsCount: parseInt(stats.posts_count),
      commentsCount: parseInt(stats.comments_count),
      groupsCount: parseInt(stats.groups_count)
    };
  }
}
