import { Router } from 'express';
import { GroupController } from '../controllers/groupController';
import { authenticate, optionalAuth } from '../middleware/auth';
import { validate, validateQuery, validateParams, schemas, paramSchemas } from '../middleware/validation';
import <PERSON><PERSON> from 'joi';

const router = Router();

// Public routes (with optional auth for member info)
router.get('/', optionalAuth, validateQuery(schemas.pagination), GroupController.getGroups);
router.get('/:id', optionalAuth, validateParams(paramSchemas.uuid), GroupController.getGroup);
router.get('/:id/members', optionalAuth, validateParams(paramSchemas.uuid), validateQuery(schemas.pagination), GroupController.getGroupMembers);

// Protected routes (require authentication)
router.post('/', authenticate, validate(schemas.createGroup), GroupController.createGroup);
router.put('/:id', authenticate, validateParams(paramSchemas.uuid), validate(schemas.updateGroup), GroupController.updateGroup);
router.delete('/:id', authenticate, validateParams(paramSchemas.uuid), GroupController.deleteGroup);

// Member management routes
router.post('/:id/join', authenticate, validateParams(paramSchemas.uuid), GroupController.joinGroup);
router.post('/:id/leave', authenticate, validateParams(paramSchemas.uuid), GroupController.leaveGroup);
router.put('/:id/members/:userId/role', authenticate, validateParams(Joi.object({
  id: Joi.string().uuid().required(),
  userId: Joi.string().uuid().required()
})), validate(Joi.object({
  role: Joi.string().valid('admin', 'moderator', 'member').required()
})), GroupController.updateMemberRole);

export default router;
