import { Request, Response } from 'express';
import { GroupService } from '../services/groupService';
import { ApiResponse, PaginatedResponse } from '../types/database';

export class GroupController {
  // Create new group
  static async createGroup(req: Request, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        } as ApiResponse);
        return;
      }

      const groupData = {
        ...req.body,
        created_by: req.user.userId
      };

      const group = await GroupService.createGroup(groupData);

      res.status(201).json({
        success: true,
        data: group,
        message: 'Group created successfully'
      } as ApiResponse);

    } catch (error) {
      console.error('Create group error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create group'
      } as ApiResponse);
    }
  }

  // Get all groups with pagination and filters
  static async getGroups(req: Request, res: Response): Promise<void> {
    try {
      const {
        page = 1,
        limit = 20,
        search,
        privacy_type,
        my_groups
      } = req.query;

      const options: {
        page: number;
        limit: number;
        search?: string;
        privacy_type?: 'public' | 'private' | 'secret';
        userId?: string;
      } = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        ...(search && { search: search as string }),
        ...(privacy_type && { privacy_type: privacy_type as 'public' | 'private' | 'secret' }),
        ...(my_groups === 'true' && req.user?.userId && { userId: req.user.userId })
      };

      const result = await GroupService.getGroups(options);
      
      const totalPages = Math.ceil(result.total / options.limit);

      res.json({
        success: true,
        data: result.groups,
        pagination: {
          page: options.page,
          limit: options.limit,
          total: result.total,
          totalPages,
          hasNext: options.page < totalPages,
          hasPrev: options.page > 1
        }
      } as PaginatedResponse);

    } catch (error) {
      console.error('Get groups error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get groups'
      } as ApiResponse);
    }
  }

  // Get single group by ID
  static async getGroup(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user?.userId;

      const result = await GroupService.getGroupWithMemberInfo(id, userId);
      
      if (!result) {
        res.status(404).json({
          success: false,
          error: 'Group not found'
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        data: {
          group: result.group,
          memberInfo: result.memberInfo,
          memberCount: result.memberCount,
          isMember: !!result.memberInfo,
          isAdmin: result.memberInfo?.role === 'admin',
          isModerator: result.memberInfo?.role === 'moderator'
        }
      } as ApiResponse);

    } catch (error) {
      console.error('Get group error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get group'
      } as ApiResponse);
    }
  }

  // Update group
  static async updateGroup(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        } as ApiResponse);
        return;
      }

      // Check if user is admin of the group
      const groupInfo = await GroupService.getGroupWithMemberInfo(id, req.user.userId!);

      if (!groupInfo || !groupInfo.memberInfo || groupInfo.memberInfo.role !== 'admin') {
        res.status(403).json({
          success: false,
          error: 'Admin access required'
        } as ApiResponse);
        return;
      }

      const updatedGroup = await GroupService.updateGroup(id, req.body);
      
      if (!updatedGroup) {
        res.status(404).json({
          success: false,
          error: 'Group not found'
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        data: updatedGroup,
        message: 'Group updated successfully'
      } as ApiResponse);

    } catch (error) {
      console.error('Update group error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update group'
      } as ApiResponse);
    }
  }

  // Join group
  static async joinGroup(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        } as ApiResponse);
        return;
      }

      const success = await GroupService.joinGroup(id, req.user.userId!);
      
      if (!success) {
        res.status(400).json({
          success: false,
          error: 'Unable to join group (already a member or group not found)'
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        message: 'Successfully joined group'
      } as ApiResponse);

    } catch (error) {
      console.error('Join group error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to join group'
      } as ApiResponse);
    }
  }

  // Leave group
  static async leaveGroup(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        } as ApiResponse);
        return;
      }

      const success = await GroupService.leaveGroup(id, req.user.userId!);
      
      res.json({
        success: true,
        message: 'Successfully left group'
      } as ApiResponse);

    } catch (error: any) {
      console.error('Leave group error:', error);
      res.status(400).json({
        success: false,
        error: error.message || 'Failed to leave group'
      } as ApiResponse);
    }
  }

  // Get group members
  static async getGroupMembers(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const {
        page = 1,
        limit = 50,
        role
      } = req.query;

      const options = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        role: role as 'admin' | 'moderator' | 'member'
      };

      const result = await GroupService.getGroupMembers(id, options);
      
      const totalPages = Math.ceil(result.total / options.limit);

      res.json({
        success: true,
        data: result.members,
        pagination: {
          page: options.page,
          limit: options.limit,
          total: result.total,
          totalPages,
          hasNext: options.page < totalPages,
          hasPrev: options.page > 1
        }
      } as PaginatedResponse);

    } catch (error) {
      console.error('Get group members error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get group members'
      } as ApiResponse);
    }
  }

  // Update member role
  static async updateMemberRole(req: Request, res: Response): Promise<void> {
    try {
      const { id, userId } = req.params;
      const { role } = req.body;
      
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        } as ApiResponse);
        return;
      }

      // Check if current user is admin of the group
      const groupInfo = await GroupService.getGroupWithMemberInfo(id, req.user.userId!);

      if (!groupInfo || !groupInfo.memberInfo || groupInfo.memberInfo.role !== 'admin') {
        res.status(403).json({
          success: false,
          error: 'Admin access required'
        } as ApiResponse);
        return;
      }

      const success = await GroupService.updateMemberRole(id, userId, role);
      
      if (!success) {
        res.status(400).json({
          success: false,
          error: 'Failed to update member role'
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        message: 'Member role updated successfully'
      } as ApiResponse);

    } catch (error) {
      console.error('Update member role error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update member role'
      } as ApiResponse);
    }
  }

  // Delete group
  static async deleteGroup(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        } as ApiResponse);
        return;
      }

      // Check if user is admin of the group
      const groupInfo = await GroupService.getGroupWithMemberInfo(id, req.user.userId!);

      if (!groupInfo || !groupInfo.memberInfo || groupInfo.memberInfo.role !== 'admin') {
        res.status(403).json({
          success: false,
          error: 'Admin access required'
        } as ApiResponse);
        return;
      }

      const success = await GroupService.deleteGroup(id);
      
      if (!success) {
        res.status(404).json({
          success: false,
          error: 'Group not found'
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        message: 'Group deleted successfully'
      } as ApiResponse);

    } catch (error) {
      console.error('Delete group error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete group'
      } as ApiResponse);
    }
  }
}
