import { Request, Response } from 'express';
import { UserService } from '../services/userService';
import { generateToken } from '../middleware/auth';
import { ApiResponse } from '../types/database';

export class AuthController {
  // Register new user
  static async register(req: Request, res: Response): Promise<void> {
    try {
      const { email, password, name, profile_url, avatar_url, location, work, bio } = req.body;

      // Check if user already exists
      const existingUser = await UserService.findByEmail(email);
      if (existingUser) {
        res.status(409).json({
          success: false,
          error: 'User with this email already exists'
        } as ApiResponse);
        return;
      }

      // Create user
      const user = await UserService.createUser({
        email,
        password,
        name,
        profile_url,
        avatar_url,
        location,
        work,
        bio
      });

      // Generate token
      const token = generateToken({
        userId: user.id,
        email: user.email,
        name: user.name,
        isAdmin: user.is_admin
      });

      // Return user data (without password)
      const { password_hash, ...userWithoutPassword } = user;

      res.status(201).json({
        success: true,
        data: {
          user: userWithoutPassword,
          token
        },
        message: 'User registered successfully'
      } as ApiResponse);

    } catch (error) {
      console.error('Registration error:', error);
      res.status(500).json({
        success: false,
        error: 'Registration failed'
      } as ApiResponse);
    }
  }

  // Login user
  static async login(req: Request, res: Response): Promise<void> {
    try {
      const { email, password } = req.body;

      // Find user
      const user = await UserService.findByEmail(email);
      if (!user) {
        res.status(401).json({
          success: false,
          error: 'Invalid email or password'
        } as ApiResponse);
        return;
      }

      // Verify password
      const isValidPassword = await UserService.verifyPassword(password, user.password_hash);
      if (!isValidPassword) {
        res.status(401).json({
          success: false,
          error: 'Invalid email or password'
        } as ApiResponse);
        return;
      }

      // Generate token
      const token = generateToken({
        userId: user.id,
        email: user.email,
        name: user.name,
        isAdmin: user.is_admin
      });

      // Return user data (without password)
      const { password_hash, ...userWithoutPassword } = user;

      res.json({
        success: true,
        data: {
          user: userWithoutPassword,
          token
        },
        message: 'Login successful'
      } as ApiResponse);

    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({
        success: false,
        error: 'Login failed'
      } as ApiResponse);
    }
  }

  // Get current user profile
  static async getProfile(req: Request, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        } as ApiResponse);
        return;
      }

      const user = await UserService.getUserProfile(req.user.userId);
      if (!user) {
        res.status(404).json({
          success: false,
          error: 'User not found'
        } as ApiResponse);
        return;
      }

      // Get user statistics
      const stats = await UserService.getUserStats(user.id);

      res.json({
        success: true,
        data: {
          user,
          stats
        }
      } as ApiResponse);

    } catch (error) {
      console.error('Get profile error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get profile'
      } as ApiResponse);
    }
  }

  // Update user profile
  static async updateProfile(req: Request, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        } as ApiResponse);
        return;
      }

      const updatedUser = await UserService.updateUser(req.user.userId, req.body);
      if (!updatedUser) {
        res.status(404).json({
          success: false,
          error: 'User not found'
        } as ApiResponse);
        return;
      }

      // Return user data (without password)
      const { password_hash, ...userWithoutPassword } = updatedUser;

      res.json({
        success: true,
        data: {
          user: userWithoutPassword
        },
        message: 'Profile updated successfully'
      } as ApiResponse);

    } catch (error) {
      console.error('Update profile error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update profile'
      } as ApiResponse);
    }
  }

  // Verify token (for frontend to check if token is still valid)
  static async verifyToken(req: Request, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Invalid token'
        } as ApiResponse);
        return;
      }

      const user = await UserService.getUserProfile(req.user.userId);
      if (!user) {
        res.status(404).json({
          success: false,
          error: 'User not found'
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        data: {
          user,
          valid: true
        }
      } as ApiResponse);

    } catch (error) {
      console.error('Token verification error:', error);
      res.status(500).json({
        success: false,
        error: 'Token verification failed'
      } as ApiResponse);
    }
  }
}
