import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';

// Generic validation middleware
export const validate = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error } = schema.validate(req.body);
    
    if (error) {
      res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      });
      return;
    }
    
    next();
  };
};

// Validation schemas
export const schemas = {
  // User schemas
  register: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(6).required(),
    name: Joi.string().min(2).max(100).required(),
    profile_url: Joi.string().uri().optional(),
    avatar_url: Joi.string().uri().optional(),
    location: Joi.string().max(255).optional(),
    work: Joi.string().max(255).optional(),
    bio: Joi.string().max(1000).optional()
  }),

  login: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().required()
  }),

  updateUser: Joi.object({
    name: Joi.string().min(2).max(100).optional(),
    profile_url: Joi.string().uri().optional(),
    avatar_url: Joi.string().uri().optional(),
    location: Joi.string().max(255).optional(),
    work: Joi.string().max(255).optional(),
    bio: Joi.string().max(1000).optional()
  }),

  // Group schemas
  createGroup: Joi.object({
    name: Joi.string().min(2).max(255).required(),
    description: Joi.string().max(2000).optional(),
    privacy_type: Joi.string().valid('public', 'private', 'secret').optional(),
    cover_image_url: Joi.string().uri().optional()
  }),

  updateGroup: Joi.object({
    name: Joi.string().min(2).max(255).optional(),
    description: Joi.string().max(2000).optional(),
    privacy_type: Joi.string().valid('public', 'private', 'secret').optional(),
    cover_image_url: Joi.string().uri().optional()
  }),

  // Post schemas
  createPost: Joi.object({
    group_id: Joi.string().uuid().required(),
    content: Joi.string().min(1).max(10000).required(),
    post_type: Joi.string().valid('text', 'image', 'video', 'link', 'poll').optional(),
    media_urls: Joi.array().items(Joi.string().uri()).optional(),
    hashtags: Joi.array().items(Joi.string().max(50)).optional()
  }),

  updatePost: Joi.object({
    content: Joi.string().min(1).max(10000).optional(),
    media_urls: Joi.array().items(Joi.string().uri()).optional(),
    hashtags: Joi.array().items(Joi.string().max(50)).optional(),
    is_pinned: Joi.boolean().optional()
  }),

  // Comment schemas
  createComment: Joi.object({
    post_id: Joi.string().uuid().required(),
    parent_comment_id: Joi.string().uuid().optional(),
    content: Joi.string().min(1).max(2000).required()
  }),

  updateComment: Joi.object({
    content: Joi.string().min(1).max(2000).required()
  }),

  // Reaction schemas
  createReaction: Joi.object({
    post_id: Joi.string().uuid().optional(),
    comment_id: Joi.string().uuid().optional(),
    reaction_type: Joi.string().valid('like', 'love', 'haha', 'wow', 'sad', 'angry').required()
  }).xor('post_id', 'comment_id'), // Either post_id or comment_id, but not both

  // Query parameter schemas
  pagination: Joi.object({
    page: Joi.number().integer().min(1).optional(),
    limit: Joi.number().integer().min(1).max(100).optional(),
    sortBy: Joi.string().optional(),
    sortOrder: Joi.string().valid('ASC', 'DESC').optional(),
    search: Joi.string().max(255).optional()
  })
};

// Validate query parameters
export const validateQuery = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error } = schema.validate(req.query);
    
    if (error) {
      res.status(400).json({
        success: false,
        error: 'Query validation error',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      });
      return;
    }
    
    next();
  };
};

// Validate URL parameters
export const validateParams = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error } = schema.validate(req.params);
    
    if (error) {
      res.status(400).json({
        success: false,
        error: 'Parameter validation error',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      });
      return;
    }
    
    next();
  };
};

// Common parameter schemas
export const paramSchemas = {
  uuid: Joi.object({
    id: Joi.string().uuid().required()
  }),
  
  userUuid: Joi.object({
    userId: Joi.string().uuid().required()
  }),
  
  groupUuid: Joi.object({
    groupId: Joi.string().uuid().required()
  }),
  
  postUuid: Joi.object({
    postId: Joi.string().uuid().required()
  }),
  
  commentUuid: Joi.object({
    commentId: Joi.string().uuid().required()
  })
};
