import api from './api';

export interface Group {
  id: string;
  name: string;
  description: string;
  privacy_type: 'public' | 'private' | 'secret';
  member_count: number;
  cover_image_url?: string;
  created_by: string;
  imported_from?: string;
  original_group_id?: string;
  created_at: string;
  updated_at: string;
  actual_member_count?: string;
}

export interface GroupMember {
  id: string;
  user_id: string;
  group_id: string;
  role: 'admin' | 'moderator' | 'member';
  joined_at: string;
  user?: {
    id: string;
    name: string;
    email: string;
    profile_image_url?: string;
  };
}

export interface GroupWithMemberInfo {
  group: Group;
  memberInfo?: GroupMember;
  memberCount: number;
}

export interface CreateGroupData {
  name: string;
  description: string;
  privacy_type: 'public' | 'private' | 'secret';
  cover_image_url?: string;
}

export interface UpdateGroupData {
  name?: string;
  description?: string;
  privacy_type?: 'public' | 'private' | 'secret';
  cover_image_url?: string;
}

export interface GroupsResponse {
  success: boolean;
  data: Group[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface GroupResponse {
  success: boolean;
  data: GroupWithMemberInfo;
}

export interface GroupMembersResponse {
  success: boolean;
  data: GroupMember[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface GroupFilters {
  page?: number;
  limit?: number;
  search?: string;
  privacy_type?: 'public' | 'private' | 'secret';
  my_groups?: boolean;
}

export class GroupService {
  // Get all groups with optional filters
  static async getGroups(filters: GroupFilters = {}): Promise<GroupsResponse> {
    const params = new URLSearchParams();

    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.search) params.append('search', filters.search);
    if (filters.privacy_type) params.append('privacy_type', filters.privacy_type);
    if (filters.my_groups) params.append('my_groups', 'true');

    const response = await api.get(`/groups?${params.toString()}`);
    return response.data;
  }

  // Get a specific group by ID
  static async getGroup(id: string): Promise<GroupResponse> {
    const response = await api.get(`/groups/${id}`);
    return response.data;
  }

  // Create a new group
  static async createGroup(data: CreateGroupData): Promise<GroupResponse> {
    const response = await api.post('/groups', data);
    return response.data;
  }

  // Update a group
  static async updateGroup(id: string, data: UpdateGroupData): Promise<GroupResponse> {
    const response = await api.put(`/groups/${id}`, data);
    return response.data;
  }

  // Delete a group
  static async deleteGroup(id: string): Promise<{ success: boolean }> {
    const response = await api.delete(`/groups/${id}`);
    return response.data;
  }

  // Join a group
  static async joinGroup(id: string): Promise<{ success: boolean }> {
    const response = await api.post(`/groups/${id}/join`);
    return response.data;
  }

  // Leave a group
  static async leaveGroup(id: string): Promise<{ success: boolean }> {
    const response = await api.post(`/groups/${id}/leave`);
    return response.data;
  }

  // Get group members
  static async getGroupMembers(
    id: string,
    filters: { page?: number; limit?: number; role?: string } = {}
  ): Promise<GroupMembersResponse> {
    const params = new URLSearchParams();

    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.role) params.append('role', filters.role);

    const response = await api.get(`/groups/${id}/members?${params.toString()}`);
    return response.data;
  }

  // Update member role
  static async updateMemberRole(
    groupId: string,
    userId: string,
    role: 'admin' | 'moderator' | 'member'
  ): Promise<{ success: boolean }> {
    const response = await api.put(`/groups/${groupId}/members/${userId}/role`, { role });
    return response.data;
  }
}