import React, { useState } from 'react';
import './App.css';
import { AuthProvider } from './contexts/AuthContext';
import Header from './components/layout/Header';
import SystemStatus from './components/common/SystemStatus';
import Groups from './pages/Groups';

function App() {
  const [currentPage, setCurrentPage] = useState<'home' | 'groups'>('home');
  const [darkMode, setDarkMode] = useState(false);

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
    document.documentElement.classList.toggle('dark');
  };

  const renderContent = () => {
    switch (currentPage) {
      case 'groups':
        return <Groups />;
      case 'home':
      default:
        return (
          <>
            {/* Hero Section */}
            <div className="mb-12 text-center">
              <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 rounded-2xl p-12 shadow-2xl">
                <div className="absolute inset-0 bg-black/10"></div>
                <div className="relative z-10">
                  <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
                    Welcome to FB Group Clone
                  </h1>
                  <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed">
                    A modern, open-source Facebook Group clone platform that seamlessly imports and displays data from the Apify Facebook Groups Scraper.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <button
                      onClick={() => setCurrentPage('groups')}
                      className="bg-white text-blue-600 hover:bg-blue-50 font-semibold py-3 px-8 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                    >
                      Get Started
                    </button>
                    <button className="bg-blue-500/20 text-white hover:bg-blue-500/30 font-semibold py-3 px-8 rounded-xl transition-all duration-200 border border-white/20 hover:border-white/40">
                      Learn More
                    </button>
                  </div>
                </div>
                {/* Decorative elements */}
                <div className="absolute top-0 left-0 w-40 h-40 bg-white/5 rounded-full -translate-x-20 -translate-y-20"></div>
                <div className="absolute bottom-0 right-0 w-60 h-60 bg-white/5 rounded-full translate-x-20 translate-y-20"></div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
                Quick Actions
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div
                  onClick={() => setCurrentPage('groups')}
                  className="group bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-gray-700 hover:border-blue-200 dark:hover:border-blue-600 cursor-pointer transform hover:-translate-y-2"
                >
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <span className="text-white text-2xl">➕</span>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                    Create Group
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                    Start a new community and bring people together around shared interests
                  </p>
                </div>

                <div className="group bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-gray-700 hover:border-green-200 dark:hover:border-green-600 cursor-pointer transform hover:-translate-y-2">
                  <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <span className="text-white text-2xl">📊</span>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                    Import Data
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                    Upload and process data from Apify Facebook Groups Scraper
                  </p>
                </div>

                <div
                  onClick={() => setCurrentPage('groups')}
                  className="group bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-gray-700 hover:border-purple-200 dark:hover:border-purple-600 cursor-pointer transform hover:-translate-y-2"
                >
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <span className="text-white text-2xl">🌐</span>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                    Browse Groups
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                    Explore and discover communities that match your interests
                  </p>
                </div>
              </div>
            </div>

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Left Column - Main Features */}
              <div className="lg:col-span-2 space-y-8">
                {/* Import Data Section */}
                <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg border border-gray-100 dark:border-gray-700">
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-4">
                      <span className="text-white text-xl">📊</span>
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                        Import Data
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        Upload JSON files from Apify scraper
                      </p>
                    </div>
                  </div>
                  <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl p-8 text-center hover:border-blue-400 dark:hover:border-blue-500 transition-colors cursor-pointer">
                    <div className="text-4xl mb-4">📁</div>
                    <p className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      Drop files here or click to browse
                    </p>
                    <p className="text-gray-600 dark:text-gray-400">
                      Supports JSON files from Apify Facebook Groups Scraper
                    </p>
                  </div>
                </div>

                {/* Manage Groups Section */}
                <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg border border-gray-100 dark:border-gray-700">
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mr-4">
                      <span className="text-white text-xl">👥</span>
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                        Manage Groups
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        Create and manage communities
                      </p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 text-center">
                      <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">0</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Active Groups</div>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 text-center">
                      <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">0</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Total Members</div>
                    </div>
                  </div>
                  <button
                    onClick={() => setCurrentPage('groups')}
                    className="w-full mt-6 bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-xl transition-colors"
                  >
                    View All Groups
                  </button>
                </div>
              </div>

              {/* Right Column - System Status & Controls */}
              <div className="space-y-8">
                <SystemStatus />

                {/* Theme Toggle */}
                <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg border border-gray-100 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                        Theme
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        Toggle dark/light mode
                      </p>
                    </div>
                    <button
                      onClick={toggleDarkMode}
                      className={`relative inline-flex h-8 w-14 items-center rounded-full transition-colors duration-200 ${
                        darkMode ? 'bg-blue-600' : 'bg-gray-300'
                      }`}
                    >
                      <span
                        className={`inline-block h-6 w-6 transform rounded-full bg-white transition-transform duration-200 shadow-lg ${
                          darkMode ? 'translate-x-7' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                </div>

                {/* Quick Stats */}
                <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg border border-gray-100 dark:border-gray-700">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">
                    Quick Stats
                  </h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 dark:text-gray-400">Total Posts</span>
                      <span className="font-semibold text-gray-900 dark:text-white">0</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 dark:text-gray-400">Comments</span>
                      <span className="font-semibold text-gray-900 dark:text-white">0</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 dark:text-gray-400">Active Users</span>
                      <span className="font-semibold text-gray-900 dark:text-white">0</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </>
        );
    }
  };

  return (
    <AuthProvider>
      <div className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 ${darkMode ? 'dark' : ''}`}>
        <Header />

        {/* Navigation */}
        {currentPage !== 'home' && (
          <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <nav className="flex space-x-8 py-4">
                <button
                  onClick={() => setCurrentPage('home')}
                  className="flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors font-medium"
                >
                  <span className="mr-2">←</span>
                  Back to Home
                </button>
                <button
                  onClick={() => setCurrentPage('groups')}
                  className={`font-medium transition-colors ${
                    currentPage === 'groups'
                      ? 'text-blue-600 dark:text-blue-400'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  Groups
                </button>
              </nav>
            </div>
          </div>
        )}

        {/* Main Content */}
        {currentPage === 'home' ? (
          <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {renderContent()}
          </main>
        ) : (
          renderContent()
        )}
      </div>
    </AuthProvider>
  );
}

export default App;
