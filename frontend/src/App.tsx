import React, { useState } from 'react';
import './App.css';
import { AuthProvider } from './contexts/AuthContext';
import Header from './components/layout/Header';
import SystemStatus from './components/common/SystemStatus';
import Groups from './pages/Groups';

function App() {
  const [currentPage, setCurrentPage] = useState<'home' | 'groups'>('home');

  const renderContent = () => {
    switch (currentPage) {
      case 'groups':
        return <Groups />;
      case 'home':
      default:
        return (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="card p-6">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Quick Actions
                </h2>
                <div className="space-y-2">
                  <button
                    onClick={() => setCurrentPage('groups')}
                    className="w-full text-left px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300"
                  >
                    Create Group
                  </button>
                  <button className="w-full text-left px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300">
                    Import Data
                  </button>
                  <button
                    onClick={() => setCurrentPage('groups')}
                    className="w-full text-left px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300"
                  >
                    Browse Groups
                  </button>
                </div>
              </div>
            </div>

            {/* Main Feed */}
            <div className="lg:col-span-3">
              <div className="space-y-6">
                {/* Welcome Card */}
                <div className="card p-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                    Welcome to FB Group Clone
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400 mb-6">
                    A modern, open-source Facebook Group clone platform that can import and display data
                    extracted from the Apify Facebook Groups Scraper.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <div className="text-2xl font-bold text-facebook-500 mb-2">📊</div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">Import Data</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Import from Apify JSON files
                      </p>
                    </div>
                    <div
                      className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg cursor-pointer hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
                      onClick={() => setCurrentPage('groups')}
                    >
                      <div className="text-2xl font-bold text-green-500 mb-2">👥</div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">Manage Groups</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Create and manage communities
                      </p>
                    </div>
                    <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <div className="text-2xl font-bold text-purple-500 mb-2">🔍</div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">Search & Filter</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Find posts and comments easily
                      </p>
                    </div>
                  </div>
                </div>

                {/* Status Card */}
                <SystemStatus />
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <AuthProvider>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />

        {/* Navigation */}
        {currentPage !== 'home' && (
          <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <nav className="flex space-x-8 py-4">
                <button
                  onClick={() => setCurrentPage('home')}
                  className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  ← Back to Home
                </button>
                <button
                  onClick={() => setCurrentPage('groups')}
                  className={`font-medium transition-colors ${
                    currentPage === 'groups'
                      ? 'text-blue-600 dark:text-blue-400'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  Groups
                </button>
              </nav>
            </div>
          </div>
        )}

        {/* Main Content */}
        {currentPage === 'home' ? (
          <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {renderContent()}
          </main>
        ) : (
          renderContent()
        )}
      </div>
    </AuthProvider>
  );
}

export default App;
