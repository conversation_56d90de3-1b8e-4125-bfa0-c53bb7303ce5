import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { authAPI, User, UserStats, setAuthToken, getAuthToken, setUser, getUser, removeAuthToken, removeUser } from '../services/api';

// Auth State Types
interface AuthState {
  user: User | null;
  stats: UserStats | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Auth Actions
type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: { user: User; stats?: UserStats } }
  | { type: 'AUTH_FAILURE'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_PROFILE'; payload: User }
  | { type: 'CLEAR_ERROR' };

// Initial State
const initialState: AuthState = {
  user: getUser(),
  stats: null,
  isAuthenticated: !!getAuthToken(),
  isLoading: false,
  error: null,
};

// Auth Reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case 'AUTH_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        stats: action.payload.stats || state.stats,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    case 'AUTH_FAILURE':
      return {
        ...state,
        user: null,
        stats: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
      };
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        stats: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };
    case 'UPDATE_PROFILE':
      return {
        ...state,
        user: action.payload,
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    default:
      return state;
  }
};

// Auth Context Type
interface AuthContextType {
  state: AuthState;
  login: (email: string, password: string) => Promise<void>;
  register: (data: {
    email: string;
    password: string;
    name: string;
    profile_url?: string;
    avatar_url?: string;
    location?: string;
    work?: string;
    bio?: string;
  }) => Promise<void>;
  logout: () => void;
  updateProfile: (data: {
    name?: string;
    profile_url?: string;
    avatar_url?: string;
    location?: string;
    work?: string;
    bio?: string;
  }) => Promise<void>;
  clearError: () => void;
  checkAuth: () => Promise<void>;
}

// Create Context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth Provider Props
interface AuthProviderProps {
  children: ReactNode;
}

// Auth Provider Component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check authentication status on mount
  useEffect(() => {
    const token = getAuthToken();
    if (token) {
      checkAuth();
    }
  }, []);

  // Login function
  const login = async (email: string, password: string): Promise<void> => {
    try {
      dispatch({ type: 'AUTH_START' });
      
      const response = await authAPI.login({ email, password });
      
      if (response.success && response.data) {
        const { user, token } = response.data;
        
        // Store token and user
        setAuthToken(token);
        setUser(user);
        
        dispatch({ type: 'AUTH_SUCCESS', payload: { user } });
      } else {
        throw new Error(response.error || 'Login failed');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || error.message || 'Login failed';
      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });
      throw error;
    }
  };

  // Register function
  const register = async (data: {
    email: string;
    password: string;
    name: string;
    profile_url?: string;
    avatar_url?: string;
    location?: string;
    work?: string;
    bio?: string;
  }): Promise<void> => {
    try {
      dispatch({ type: 'AUTH_START' });
      
      const response = await authAPI.register(data);
      
      if (response.success && response.data) {
        const { user, token } = response.data;
        
        // Store token and user
        setAuthToken(token);
        setUser(user);
        
        dispatch({ type: 'AUTH_SUCCESS', payload: { user } });
      } else {
        throw new Error(response.error || 'Registration failed');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || error.message || 'Registration failed';
      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });
      throw error;
    }
  };

  // Logout function
  const logout = (): void => {
    removeAuthToken();
    removeUser();
    dispatch({ type: 'LOGOUT' });
  };

  // Update profile function
  const updateProfile = async (data: {
    name?: string;
    profile_url?: string;
    avatar_url?: string;
    location?: string;
    work?: string;
    bio?: string;
  }): Promise<void> => {
    try {
      const response = await authAPI.updateProfile(data);
      
      if (response.success && response.data) {
        const { user } = response.data;
        setUser(user);
        dispatch({ type: 'UPDATE_PROFILE', payload: user });
      } else {
        throw new Error(response.error || 'Profile update failed');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || error.message || 'Profile update failed';
      throw new Error(errorMessage);
    }
  };

  // Check authentication status
  const checkAuth = async (): Promise<void> => {
    try {
      const response = await authAPI.getProfile();
      
      if (response.success && response.data) {
        const { user, stats } = response.data;
        setUser(user);
        dispatch({ type: 'AUTH_SUCCESS', payload: { user, stats } });
      } else {
        throw new Error('Authentication check failed');
      }
    } catch (error) {
      // If auth check fails, clear stored data
      removeAuthToken();
      removeUser();
      dispatch({ type: 'LOGOUT' });
    }
  };

  // Clear error function
  const clearError = (): void => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const contextValue: AuthContextType = {
    state,
    login,
    register,
    logout,
    updateProfile,
    clearError,
    checkAuth,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
