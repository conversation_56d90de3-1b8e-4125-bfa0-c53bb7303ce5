import React, { useState, useEffect } from 'react';
import { systemAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

interface SystemStatusProps {
  className?: string;
}

interface HealthStatus {
  status: string;
  timestamp: string;
  environment: string;
  database: string;
}

const SystemStatus: React.FC<SystemStatusProps> = ({ className = '' }) => {
  const { state } = useAuth();
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const checkHealth = async () => {
    try {
      setError(null);
      const response = await systemAPI.healthCheck();
      if (response.success && response.data) {
        setHealthStatus(response.data);
      }
    } catch (err) {
      setError('Failed to connect to backend');
      setHealthStatus(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    checkHealth();
    
    // Check health every 30 seconds
    const interval = setInterval(checkHealth, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const getStatusBadge = (status: string, label: string) => {
    const isConnected = status === 'connected' || status === 'OK';
    const badgeClass = isConnected
      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';

    const icon = isConnected ? '✓' : '✗';

    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${badgeClass}`}>
        {icon} {label}
      </span>
    );
  };

  const getAuthStatusBadge = () => {
    if (state.isAuthenticated) {
      return (
        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
          ✓ Authenticated
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
          ⚠ Not Authenticated
        </span>
      );
    }
  };

  if (isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg border border-gray-100 dark:border-gray-700 ${className}`}>
        <div className="flex items-center mb-6">
          <div className="w-12 h-12 bg-gradient-to-br from-gray-400 to-gray-500 rounded-xl flex items-center justify-center mr-4">
            <span className="text-white text-xl">⚡</span>
          </div>
          <div>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
              System Status
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Checking system health...
            </p>
          </div>
        </div>
        <div className="animate-pulse space-y-4">
          <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
            <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-24"></div>
            <div className="h-6 bg-gray-200 dark:bg-gray-600 rounded w-20"></div>
          </div>
          <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
            <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-20"></div>
            <div className="h-6 bg-gray-200 dark:bg-gray-600 rounded w-24"></div>
          </div>
          <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
            <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-28"></div>
            <div className="h-6 bg-gray-200 dark:bg-gray-600 rounded w-28"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg border border-gray-100 dark:border-gray-700 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-4">
            <span className="text-white text-xl">⚡</span>
          </div>
          <div>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
              System Status
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Real-time system health
            </p>
          </div>
        </div>
        <button
          onClick={checkHealth}
          className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          title="Refresh status"
        >
          <span className="text-lg">🔄</span>
        </button>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
          <div className="flex items-center">
            <span className="text-lg mr-3">🌐</span>
            <span className="font-medium text-gray-900 dark:text-white">Backend API</span>
          </div>
          {error ? (
            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
              ✗ Disconnected
            </span>
          ) : (
            getStatusBadge(healthStatus?.status || '', 'Connected')
          )}
        </div>

        <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
          <div className="flex items-center">
            <span className="text-lg mr-3">🗄️</span>
            <span className="font-medium text-gray-900 dark:text-white">Database</span>
          </div>
          {error ? (
            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400">
              ? Unknown
            </span>
          ) : (
            getStatusBadge(healthStatus?.database || '', 'Connected')
          )}
        </div>

        <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
          <div className="flex items-center">
            <span className="text-lg mr-3">🔐</span>
            <span className="font-medium text-gray-900 dark:text-white">Authentication</span>
          </div>
          {getAuthStatusBadge()}
        </div>

        {healthStatus && !error && (
          <div className="pt-4 border-t border-gray-200 dark:border-gray-600">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500 dark:text-gray-400">Environment</span>
                <div className="font-medium text-gray-900 dark:text-white">{healthStatus.environment}</div>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Last updated</span>
                <div className="font-medium text-gray-900 dark:text-white">
                  {new Date(healthStatus.timestamp).toLocaleTimeString()}
                </div>
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl">
            <div className="flex items-center">
              <span className="text-red-500 text-lg mr-3">⚠️</span>
              <div className="text-sm text-red-600 dark:text-red-400 font-medium">
                {error}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SystemStatus;
