import React, { useState, useEffect } from 'react';
import { systemAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

interface SystemStatusProps {
  className?: string;
}

interface HealthStatus {
  status: string;
  timestamp: string;
  environment: string;
  database: string;
}

const SystemStatus: React.FC<SystemStatusProps> = ({ className = '' }) => {
  const { state } = useAuth();
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const checkHealth = async () => {
    try {
      setError(null);
      const response = await systemAPI.healthCheck();
      if (response.success && response.data) {
        setHealthStatus(response.data);
      }
    } catch (err) {
      setError('Failed to connect to backend');
      setHealthStatus(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    checkHealth();
    
    // Check health every 30 seconds
    const interval = setInterval(checkHealth, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const getStatusBadge = (status: string, label: string) => {
    const isConnected = status === 'connected' || status === 'OK';
    const badgeClass = isConnected
      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
    
    const icon = isConnected ? '✓' : '✗';
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badgeClass}`}>
        {icon} {label}
      </span>
    );
  };

  const getAuthStatusBadge = () => {
    if (state.isAuthenticated) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
          ✓ Authenticated
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
          ⚠ Not Authenticated
        </span>
      );
    }
  };

  if (isLoading) {
    return (
      <div className={`card p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          System Status
        </h3>
        <div className="animate-pulse space-y-3">
          <div className="flex items-center justify-between">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
          </div>
          <div className="flex items-center justify-between">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
          </div>
          <div className="flex items-center justify-between">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-28"></div>
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-28"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`card p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          System Status
        </h3>
        <button
          onClick={checkHealth}
          className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          title="Refresh status"
        >
          🔄
        </button>
      </div>
      
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-gray-600 dark:text-gray-400">Backend API</span>
          {error ? (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
              ✗ Disconnected
            </span>
          ) : (
            getStatusBadge(healthStatus?.status || '', 'Connected')
          )}
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-gray-600 dark:text-gray-400">Database</span>
          {error ? (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400">
              ? Unknown
            </span>
          ) : (
            getStatusBadge(healthStatus?.database || '', 'Connected')
          )}
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-gray-600 dark:text-gray-400">Authentication</span>
          {getAuthStatusBadge()}
        </div>
        
        {healthStatus && !error && (
          <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Environment: {healthStatus.environment}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Last updated: {new Date(healthStatus.timestamp).toLocaleTimeString()}
            </div>
          </div>
        )}
        
        {error && (
          <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
            <div className="text-xs text-red-500 dark:text-red-400">
              {error}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SystemStatus;
