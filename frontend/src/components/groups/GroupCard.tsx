import React from 'react';
import { Group } from '../../services/groupService';

interface GroupCardProps {
  group: Group;
  onJoin?: (groupId: string) => void;
  onLeave?: (groupId: string) => void;
  onView?: (groupId: string) => void;
  isJoined?: boolean;
  isLoading?: boolean;
}

const GroupCard: React.FC<GroupCardProps> = ({
  group,
  onJoin,
  onLeave,
  onView,
  isJoined = false,
  isLoading = false,
}) => {
  const getPrivacyIcon = () => {
    switch (group.privacy_type) {
      case 'public':
        return '🌐';
      case 'private':
        return '🔒';
      case 'secret':
        return '👁️';
      default:
        return '🌐';
    }
  };

  const getPrivacyLabel = () => {
    switch (group.privacy_type) {
      case 'public':
        return 'Public';
      case 'private':
        return 'Private';
      case 'secret':
        return 'Secret';
      default:
        return 'Public';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const handleAction = () => {
    if (isJoined && onLeave) {
      onLeave(group.id);
    } else if (!isJoined && onJoin) {
      onJoin(group.id);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
      {/* Cover Image */}
      <div className="h-32 bg-gradient-to-r from-blue-500 to-purple-600 rounded-t-lg relative overflow-hidden">
        {group.cover_image_url ? (
          <img
            src={group.cover_image_url}
            alt={group.name}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-r from-blue-500 to-purple-600" />
        )}

        {/* Privacy Badge */}
        <div className="absolute top-3 right-3">
          <div className="inline-flex items-center px-2 py-1 bg-black/20 backdrop-blur-sm rounded-full text-white text-xs font-medium">
            <span className="mr-1">{getPrivacyIcon()}</span>
            <span>{getPrivacyLabel()}</span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {/* Group Name */}
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 line-clamp-1">
          {group.name}
        </h3>

        {/* Description */}
        <p className="text-gray-600 dark:text-gray-400 text-sm mb-3 line-clamp-2">
          {group.description}
        </p>

        {/* Stats */}
        <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
          <div className="flex items-center">
            <span className="mr-1">👥</span>
            <span>{group.member_count} members</span>
          </div>

          <div className="flex items-center">
            <span className="mr-1">📅</span>
            <span>Created {formatDate(group.created_at)}</span>
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-2">
          <button
            onClick={() => onView && onView(group.id)}
            className="flex-1 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
          >
            View
          </button>

          {(onJoin || onLeave) && (
            <button
              onClick={handleAction}
              disabled={isLoading}
              className={`flex-1 px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                isJoined
                  ? 'text-red-700 dark:text-red-300 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30'
                  : 'text-blue-700 dark:text-blue-300 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30'
              } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {isLoading ? 'Loading...' : isJoined ? 'Leave' : 'Join'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default GroupCard;